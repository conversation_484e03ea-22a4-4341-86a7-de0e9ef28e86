#include "sh367601xb_conversion.h"



/* ==========================================ntc========================================== */
/**
 *  @brief 阻值 <-> 温度 对照表
 */
const NTC_TypeDef ntc3435[150] = 
{
    {50, 366.4286}, {51, 344.5753}, {52, 324.1796}, {53, 305.1344}, {54, 287.3413},
    {55, 270.7097}, {56, 255.1561}, {57, 240.6035}, {58, 226.9810}, {59, 214.2231},
    {60, 202.2693}, {61, 191.0637}, {62, 180.5546}, {63, 170.6944}, {64, 161.4387},
    {65, 152.7468}, {66, 144.5807}, {67, 136.9052}, {68, 129.6879}, {69, 122.8985},
    {70, 116.5089}, {71, 110.4932}, {72, 104.8272}, {73, 99.4883},  {74, 94.4558},
    {75, 89.7101}, {76, 85.2332},  {77, 81.0082},  {78, 77.0194},  {79, 73.2523},
    {80, 69.6931}, {81, 66.3291},  {82, 63.1485},  {83, 60.1402},  {84, 57.2939},
    {85, 54.5998}, {86, 52.049},   {87, 49.633},   {88, 47.3439},  {89, 45.1743},
    {90, 43.1172}, {91, 41.1663},  {92, 39.3153},  {93, 37.5587},  {94, 35.891},
    {95, 34.3074}, {96, 32.8029},  {97, 31.3734},  {98, 30.0145},  {99, 28.7225},
    {100, 27.4936}, {101, 26.3245}, {102, 25.2119}, {103, 24.1527}, {104, 23.1442},
    {105, 22.1835}, {106, 21.2682}, {107, 20.3959}, {108, 19.5644}, {109, 18.7714},
    {110, 18.0151}, {111, 17.2935}, {112, 16.6048}, {113, 15.9475}, {114, 15.3198},
    {115, 14.7203}, {116, 14.1475}, {117, 13.6003}, {118, 13.0772}, {119, 12.5771},
    {120, 12.0988}, {121, 11.6413}, {122, 11.2037}, {123, 10.7848}, {124, 10.3839},
    {125, 10.00},   {127, 9.2802},  {128, 8.9428},  {129, 8.6195},  {130, 8.3096},
    {131, 8.0124},  {132, 7.7275},  {133, 7.4541},  {134, 7.1919},  {135, 6.9403},
    {136, 6.6987},  {137, 6.4669},  {138, 6.2442},  {139, 6.0304},  {140, 5.825},
    {141, 5.6276},  {142, 5.438},   {143, 5.2557},  {144, 5.0804},  {145, 4.9119},
    {146, 4.7498},  {147, 4.5939},  {148, 4.4439},  {149, 4.2995},  {150, 4.1605},
    {151, 4.0268},  {152, 3.898},   {153, 3.7739},  {154, 3.6544},  {155, 3.5393},
    {156, 3.4284},  {157, 3.3215},  {158, 3.2185},  {159, 3.1191},  {160, 3.0234},
    {161, 2.931},   {162, 2.8419},  {163, 2.7559},  {164, 2.6729},  {165, 2.5929},
    {166, 2.5156},  {167, 2.441},   {168, 2.369},   {169, 2.2994},  {170, 2.2322},
    {171, 2.1673},  {172, 2.1046},  {173, 2.044},   {174, 1.9854},  {175, 1.9288},
    {176, 1.874},   {177, 1.8211},  {178, 1.7699},  {179, 1.7204},  {180, 1.6725},
    {181, 1.6262},  {182, 1.5813},  {183, 1.5379},  {184, 1.4959},  {185, 1.4553},
    {186, 1.4159},  {187, 1.3778},  {188, 1.3408},  {189, 1.3051},  {190, 1.2704},
    {191, 1.2368},  {192, 1.2043},  {193, 1.1728},  {194, 1.1422},  {195, 1.1126},
    {196, 1.0839},  {197, 1.056},   {198, 1.029},   {199, 1.0028},  {200, 0.9774},
};

/**
 * @brief 温度表大小
 */

/**
 * @brief  通过查表法计算NTC温度（新版本，支持自定义温度偏移）
 * @param  resistance_ohm 当前NTC阻值，单位：欧姆（Ω），范围：0.1-1000.0
 * @param  temp_offset_celsius 温度偏移量，单位：摄氏度（℃），通常为100用于数据存储优化
 * @param  ntc_table NTC查找表指针，不能为NULL
 * @param  table_size 查找表大小，必须大于0
 * @return 温度值，单位：摄氏度（℃），失败返回-128
 */
char ntc_calculate_temp_from_resistance(double resistance_ohm, char temp_offset_celsius,
                                       const NTC_TypeDef *ntc_table, unsigned short table_size)
{
    /* 参数验证 */
    if (ntc_table == 0 || table_size == 0 || resistance_ohm <= 0.0) {
        return -128; /* 错误返回值 */
    }

    /* 边界检查 */
    if (resistance_ohm >= ntc_table[0].resist) return ntc_table[0].temp - temp_offset_celsius;
    if (resistance_ohm <= ntc_table[table_size-1].resist) return ntc_table[table_size-1].temp - temp_offset_celsius;

    /* 二分查找 */
    unsigned short left = 0;
    unsigned short right = table_size - 1;
    while (left <= right)
    {
        unsigned short mid = left + (right - left) / 2;
        if (ntc_table[mid].resist == resistance_ohm)
        {
            return ntc_table[mid].temp - temp_offset_celsius;
        }
        else if (ntc_table[mid].resist < resistance_ohm)
        {
            right = mid - 1;
        }
        else
        {
            left = mid + 1;
        }
    }

    /* 返回最近的温度点（右边界） */
    return ntc_table[right].temp - temp_offset_celsius;
}


/**
 * @brief  ADC寄存器值转换为高温温度（简化版本）
 * @param  adc_reg_value ADC温度寄存器值，范围：0x00-0x1FF（0-511）
 * @param  ntc_table NTC查找表指针，不能为NULL
 * @param  table_size 查找表大小，必须大于0
 * @return 温度值，单位：摄氏度（℃），失败返回-128
 */
char ntc_calculate_temp_from_adc(unsigned short adc_reg_value, const NTC_TypeDef *ntc_table, unsigned short table_size)
{
    /* 参数验证 */
    if (ntc_table == 0 || table_size == 0 || adc_reg_value >= 512) {
        return -128; /* 错误返回值 */
    }

    /* 计算NTC阻值：RT = (10kΩ * reg_value) / (512 - reg_value) */
    /* 使用固定参数：pullup_resistance=10kΩ, adc_resolution=512 */
    double rt_kohm = (10.0 * adc_reg_value) / ((double)512 - adc_reg_value);
    return ntc_calculate_temp_from_resistance(rt_kohm, 100, ntc_table, table_size);
}


/**
 * @brief  低温保护寄存器值转换为温度（简化版本）
 * @param  reg_value 寄存器值，范围：0x00-0xFF（0-255）
 * @param  ntc_table NTC查找表指针，不能为NULL
 * @param  table_size 查找表大小，必须大于0
 * @return 温度值，单位：摄氏度（℃），失败返回-128
 */
char ntc_calculate_low_temp_from_reg(unsigned char reg_value, const NTC_TypeDef *ntc_table, unsigned short table_size)
{
    /* 参数验证 */
    if (ntc_table == 0 || table_size == 0) {
        return -128; /* 错误返回值 */
    }

    /* 使用固定参数：pullup_resistance=10kΩ, adc_resolution=512, voltage_offset=0.5V */
    double voltage_ratio = (double)reg_value / 512.0 + 0.5;
    if (voltage_ratio >= 1.0) {
        return -128; /* 避免除零错误 */
    }

    double rt_kohm = (10.0 * voltage_ratio) / (1.0 - voltage_ratio);
    return ntc_calculate_temp_from_resistance(rt_kohm, 100, ntc_table, table_size);
}

/**
 * @brief  外部温度寄存器值转换温度（新版本，支持自定义参数）
 * @param  temp_reg 温度寄存器值，范围：0x00-0x7FFF（0-32767）
 * @param  max_reg_value 最大寄存器值，通常为32768（15位ADC）
 * @param  ntc_table NTC查找表指针，不能为NULL
 * @param  table_size 查找表大小，必须大于0
 * @return 温度值，单位：摄氏度（℃），失败返回-128
 */
char ntc_calculate_external_temp(unsigned short temp_reg, const NTC_TypeDef *ntc_table, unsigned short table_size)
{
    /* 参数验证 */
    if (ntc_table == 0 || table_size == 0 || temp_reg >= 32768) {
        return -128; /* 错误返回值 */
    }

    /* R_T = (TEMP / (32768 - TEMP)) × 10kΩ */
    /* 使用固定参数：max_reg_value=32768, pullup_resistance=10kΩ */
    double rt_kohm = ((double)temp_reg / (32768 - temp_reg)) * 10.0;
    return ntc_calculate_temp_from_resistance(rt_kohm, 100, ntc_table, table_size);
}


/**
 * @brief  从温度查表获取电阻值（新版本，支持自定义参数）
 * @param  temp_celsius 当前温度，单位：摄氏度（℃），范围：-50到200
 * @param  ntc_table NTC查找表指针，不能为NULL
 * @param  table_size 查找表大小，必须大于0
 * @return 阻值，单位：千欧（kΩ），失败返回-1.0
 */
double ntc_find_resistance_from_temp(char temp_celsius, char temp_offset_celsius,
                                    const NTC_TypeDef *ntc_table, unsigned short table_size)
{
    /* 参数验证 */
    if (ntc_table == 0 || table_size == 0) {
        return -1.0; /* 错误返回值 */
    }

    char adjusted_temp = temp_celsius + temp_offset_celsius;

    /* 边界检查 */
    if (adjusted_temp <= ntc_table[0].temp) return ntc_table[0].resist;
    if (adjusted_temp >= ntc_table[table_size - 1].temp) return ntc_table[table_size - 1].resist;

    /* 二分查找 */
    int left = 0;
    int right = table_size - 1;

    while (left <= right)
    {
        int mid = left + (right - left) / 2;
        if (ntc_table[mid].temp == adjusted_temp)
        {
            return ntc_table[mid].resist;
        }
        else if (ntc_table[mid].temp < adjusted_temp)
        {
            left = mid + 1;
        }
        else
        {
            right = mid - 1;
        }
    }

    /* 返回最近的温度点对应的电阻值 */
    return ntc_table[right].resist;
}

/**
 * @brief  从温度查表获取电阻 R_T (kΩ)（兼容版本）
 * @param  temp 当前温度（℃）
 * @return 阻值（Ω）
 */
double find_rt_from_temp(char temp)
{
    return ntc_find_resistance_from_temp(temp, 100, ntc3435, NTC_TABLE_SIZE);
}
/**
 * @brief  温度转换为高温保护寄存器值（新版本，支持自定义参数）
 * @param  temp_celsius 当前温度，单位：摄氏度（℃），范围：-50到200
 * @param  ntc_table NTC查找表指针，不能为NULL
 * @param  table_size 查找表大小，必须大于0
 * @return 寄存器值，范围：0-255，失败返回255
 */
unsigned char ntc_calculate_high_temp_reg(char temp_celsius, const NTC_TypeDef *ntc_table, unsigned short table_size)
{
    /* 参数验证 */
    if (ntc_table == 0 || table_size == 0) {
        return 255; /* 错误返回值 */
    }

    /* 获取对应温度的电阻值 */
    double rt_kohm = ntc_find_resistance_from_temp(temp_celsius, 100, ntc_table, table_size);
    if (rt_kohm < 0.0) {
        return 255; /* 错误返回值 */
    }

    /* 高温保护寄存器值计算：使用固定参数 */
    /* 上拉电阻：10kΩ，ADC分辨率：512 */
    double ratio = rt_kohm / (10.0 + rt_kohm);
    double reg_float = ratio * 512.0;

    /* 边界检查和四舍五入 */
    if (reg_float > 255.0) return 255;
    if (reg_float < 0.0) return 0;
    return (unsigned char)(reg_float + 0.5);
}

/**
 * @brief  温度转换为低温保护寄存器值（简化版本）
 * @param  temp_celsius 当前温度，单位：摄氏度（℃），范围：-50到200
 * @param  ntc_table NTC查找表指针，不能为NULL
 * @param  table_size 查找表大小，必须大于0
 * @return 寄存器值，范围：0-255，失败返回255
 */
unsigned char ntc_calculate_low_temp_reg(char temp_celsius, const NTC_TypeDef *ntc_table, unsigned short table_size)
{
    /* 参数验证 */
    if (ntc_table == 0 || table_size == 0) {
        return 255; /* 错误返回值 */
    }

    /* 获取对应温度的电阻值 */
    double rt_kohm = ntc_find_resistance_from_temp(temp_celsius, 100, ntc_table, table_size);
    if (rt_kohm < 0.0) {
        return 255; /* 错误返回值 */
    }

    /* 低温保护寄存器值计算：使用固定参数 */
    /* 上拉电阻：10kΩ，ADC分辨率：512，电压偏移：0.5V */
    double ratio = rt_kohm / (10.0 + rt_kohm);
    double reg_float = (ratio - 0.5) * 512.0;

    /* 边界检查和四舍五入 */
    if (reg_float > 255.0) return 255;
    if (reg_float < 0.0) return 0;
    return (unsigned char)(reg_float + 0.5);
}

/**
 * @brief  充电高温...转换寄存器值（兼容版本）
 * @param  temp 当前温度（℃）
 * @return 寄存器值
 */
unsigned char calculate_high_temp_reg(char temp)
{
    return ntc_calculate_high_temp_reg(temp, ntc3435, NTC_TABLE_SIZE);
}

/**
 * @brief  充电低温...转换寄存器值（兼容版本）
 * @param  temp 当前温度（℃）
 * @return 寄存器值
 */
unsigned char calculate_reg_from_rt(char temp)
{
    return ntc_calculate_low_temp_reg(temp, ntc3435, NTC_TABLE_SIZE);
}



/* ==========================================平滑滤波========================================== */
#define N 10
static float sma_buffer[N] = {0};
static int sma_index = 0;
static float ema = 0;

/**
 * @brief  平滑滤波
 * @param  new_sample 当前值
 * @return 滤波后的值
 */
float hybridFilter(float new_sample)
{
    /* EMA系数 */
    float alpha = 0.35;      
    /* SMA阶段 */
    float sum = 0;
    sma_buffer[sma_index] = new_sample;
    sma_index = (sma_index + 1) % N;
    for (int i = 0; i < N; i++) sum += sma_buffer[i];
    float sma = sum / N;
    
    /* EMA阶段 */
    ema = alpha * sma + (1 - alpha) * ema;
    return ema;
}



/* ==========================================电流========================================== */
/**
 * @brief  ADC寄存器值转换为电流（新版本，支持自定义参数）
 * @param  adc_reg_value ADC电流寄存器值，范围：0-65535
 * @param  sampling_resistance_mohm 采样电阻值，单位：毫欧（mΩ），通常为0.5-10mΩ
 * @param  adc_reference_factor ADC参考因子，通常为26214.4
 * @return 电流值，单位：毫安（mA），失败返回-32768
 */
int current_calculate_from_adc(unsigned short adc_reg_value, float sampling_resistance_mohm,
                              float adc_gain, float adc_reference_factor)
{
    /* 参数验证 */
    if (sampling_resistance_mohm <= 0.0f || adc_reference_factor <= 0.0f || adc_gain <= 0.0f) {
        return -32768; /* 错误返回值 */
    }

    /* 电流计算：I = (adc_gain * adc_reg_value) / (adc_reference_factor * sampling_resistance) */
    float current_ma = (adc_gain * adc_reg_value) / (adc_reference_factor * sampling_resistance_mohm);

    return (int)current_ma;
}

/**
 * @brief  电流温度补偿（新版本，支持自定义参数）
 * @param  current_ma 当前电流，单位：毫安（mA）
 * @param  temp_celsius 当前温度，单位：摄氏度（℃），范围：-40到125
 * @param  reference_temp_celsius 参考温度，单位：摄氏度（℃），通常为25℃
 * @param  temp_coefficient 温度系数，单位：1/℃，通常为0.0039
 * @param  temp_min_celsius 最低工作温度，单位：摄氏度（℃）
 * @param  temp_max_celsius 最高工作温度，单位：摄氏度（℃）
 * @return 补偿后电流，单位：毫安（mA）
 */
float current_apply_temp_compensation(float current_ma, float temp_celsius,
                                     float reference_temp_celsius, float temp_coefficient,
                                     float temp_min_celsius, float temp_max_celsius)
{
    /* 参数验证 */
    if (temp_coefficient <= 0.0f) {
        return current_ma; /* 无效温度系数，返回原值 */
    }

    /* 温度边界限制 */
    if (temp_celsius < temp_min_celsius) temp_celsius = temp_min_celsius;
    if (temp_celsius > temp_max_celsius) temp_celsius = temp_max_celsius;

    /* 温度补偿计算 */
    float delta_temp = temp_celsius - reference_temp_celsius;
    float compensation_factor = 1.0f + temp_coefficient * delta_temp;

    /* 避免除零错误 */
    if (compensation_factor <= 0.0f) {
        return current_ma;
    }

    return current_ma / compensation_factor;
}





/* ==========================================电压========================================== */


/**
 * @brief  寄存器值转换成电压（简化版本）
 * @param  cell 电压寄存器值
 * @return 电压，单位：毫伏（mV）
 */
unsigned short Reg_From_Voltage(unsigned short cell)
{
    /* 直接计算：(cell * 5.0) / 32 * 1000 = cell * 0.15625 */
    return (unsigned short)((cell * 5) / 32);
}




/* ==========================================SOC========================================== */
/**
 * @brief  毫安（mA）和毫秒（ms）转换为SOC百分比变化量
 * @param  current_mA 电流（mA），充电为负，放电为正
 * @param  time_ms    时间间隔（ms）
 * @param  capacity_mAh 电池总容量（mAh）
 * @return SOC变化量（%）
 */
float calculate_soc_delta(float current_mA, float time_ms, float capacity_mAh)
{
    /* 单位转换：(mA × ms) → (mAh × 3600 × 1000) → % */
    float delta_soc = (current_mA * time_ms) / (capacity_mAh * 3600.0f * 1000.0f) * 100.0f;
    return delta_soc;
}
/**
 * @brief  更新SOC（不考虑效率）
 * @param  soc         当前SOC（%）
 * @param  capacity_mAh 电池容量（mAh）
 * @param  current_mA  当前电流（mA）
 * @param  time_ms     时间间隔（ms）
 * @return 更新后的SOC（%）
 */
float update_soc_simple(float soc, float capacity_mAh, float current_mA, float time_ms, unsigned char direction)
{
    /* 1. 计算SOC变化量 */
    float delta_soc = calculate_soc_delta(current_mA, time_ms, capacity_mAh);
    
    /* 2. 更新SOC */
    if (1 == direction) soc -= delta_soc;
    else soc += delta_soc;
    
    /* 3. SOC边界保护 */
    if (soc > 100.0f) soc = 100.0f;
    else if (soc < 0.0f) soc = 0.0f;
    
    return soc;
}


