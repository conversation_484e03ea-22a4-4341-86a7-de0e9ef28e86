#include "tl_common.h"
#include "sh367601xb_conversion.h"
#include "sh367601xb.h"
#include "../uart/app_usart.h"
#include "../bms_data.h"


/* ==========================================配置管理模块实现========================================== */

/**
 * @brief 设置设备ID
 * @param self 设备实例指针
 * @param id 设备ID，范围：0x00-0xFF
 */
static void sh367601b_config_set_id(*********_Device* self, unsigned char id)
{
    if (self == NULL) return;
    self->rom.id = id;
    self->write_flags[0] = 1;
}

/**
 * @brief 设置放电MOSFET强制开启恢复延时控制位
 * @param self 设备实例指针
 * @param enmosr 控制位，0=禁用，1=启用
 */
static void sh367601b_config_set_enmosr(*********_Device* self, unsigned char enmosr)
{
    if (self == NULL || enmosr > 0x01) return;
    self->rom.enmosr = enmosr;
    self->write_flags[1] = 1;
}

/**
 * @brief 设置退出过充电保护及充电高低温保护功能变化控制位
 * @param self 设备实例指针
 * @param chys 控制位，0=禁用，1=启用
 */
static void sh367601b_config_set_chys(*********_Device* self, unsigned char chys)
{
    if (self == NULL || chys > 0x01) return;
    self->rom.chys = chys;
    self->write_flags[1] = 1;
}

/**
 * @brief 设置充电状态下充电温度保护延时（配置chys为1时才生效）
 * @param self 设备实例指针
 * @param tc 延时时间，单位：秒，范围：3-17秒，步长2秒
 */
static void sh367601b_config_set_tc(*********_Device* self, unsigned char tc)
{
    if (self == NULL) return;
    self->rom.tc = (tc - 3) / 2;
    self->write_flags[1] = 1;
}

/**
 * @brief 设置电池串数配置
 * @param self 设备实例指针
 * @param cn 电池串数，范围：6-21串
 */
static void sh367601b_config_set_cn(*********_Device* self, unsigned char cn)
{
    if (self == NULL) return;
    self->rom.cn = cn - 6;
    self->write_flags[1] = 1;
}

/**
 * @brief 设置均衡开启条件控制位
 * @param self 设备实例指针
 * @param bals 均衡条件，0=充电时均衡，1=静置时均衡
 */
static void sh367601b_config_set_bals(*********_Device* self, unsigned char bals)
{
    if (self == NULL || bals > 0x01) return;
    self->rom.bals = bals;
    self->write_flags[2] = 1;
}

/**
 * @brief 设置充电状态检测电压设置位
 * @param self 设备实例指针
 * @param chs 检测电压，0=100mV，1=200mV
 */
static void sh367601b_config_set_chs(*********_Device* self, unsigned char chs)
{
    if (self == NULL || chs > 0x01) return;
    self->rom.chs = chs;
    self->write_flags[2] = 1;
}

/**
 * @brief 设置放电过流保护自恢复使能位
 * @param self 设备实例指针
 * @param ocra 自恢复使能，0=禁用，1=启用
 */
static void sh367601b_config_set_ocra(*********_Device* self, unsigned char ocra)
{
    if (self == NULL || ocra > 0x01) return;
    self->rom.ocra = ocra;
    self->write_flags[2] = 1;
}

/**
 * @brief 设置过充电保护恢复条件设置位
 * @param self 设备实例指针
 * @param eovr 恢复条件，0=任意单体电压低于恢复电压，1=所有单体电压低于恢复电压
 */
static void sh367601b_config_set_eovr(*********_Device* self, unsigned char eovr)
{
    if (self == NULL || eovr > 0x01) return;
    self->rom.eovr = eovr;
    self->write_flags[2] = 1;
}

/**
 * @brief 设置过放电保护恢复条件设置位
 * @param self 设备实例指针
 * @param euvr 恢复条件，0=任意单体电压高于恢复电压，1=所有单体电压高于恢复电压
 */
static void sh367601b_config_set_euvr(*********_Device* self, unsigned char euvr)
{
    if (self == NULL || euvr > 0x01) return;
    self->rom.euvr = euvr;
    self->write_flags[2] = 1;
}

/**
 * @brief 设置断线保护使能位
 * @param self 设备实例指针
 * @param eow 断线保护，0=禁用，1=启用
 */
static void sh367601b_config_set_eow(*********_Device* self, unsigned char eow)
{
    if (self == NULL || eow > 0x01) return;
    self->rom.eow = eow;
    self->write_flags[2] = 1;
}

/**
 * @brief 设置TS3温度检测点功能设置位
 * @param self 设备实例指针
 * @param eot3 TS3功能，0=外部温度检测，1=内部温度检测
 */
static void sh367601b_config_set_eot3(*********_Device* self, unsigned char eot3)
{
    if (self == NULL || eot3 > 0x01) return;
    self->rom.eot3 = eot3;
    self->write_flags[2] = 1;
}

/**
 * @brief 设置充放电MOSFET强制开启使能位
 * @param self 设备实例指针
 * @param enmos MOSFET强制开启，0=禁用，1=启用
 */
static void sh367601b_config_set_enmos(*********_Device* self, unsigned char enmos)
{
    if (self == NULL || enmos > 0x01) return;
    self->rom.enmos = enmos;
    self->write_flags[2] = 1;
}

/**
 * @brief 设置过充电保护延时
 * @param self 设备实例指针
 * @param ovt 延时设置，0=1s，1=2s，2=4s，3=8s
 */
static void sh367601b_config_set_ovt(*********_Device* self, unsigned char ovt)
{
    if (self == NULL || ovt > 0x03) return;
    self->rom.ovt = ovt;
    self->write_flags[3] = 1;
}

/**
 * @brief 设置过充电保护电压
 * @param self 设备实例指针
 * @param ov 过充电保护电压，单位：mV，范围：3000-5000mV，步长5mV
 */
static void sh367601b_config_set_ov(*********_Device* self, unsigned short ov)
{
    if (self == NULL) return;
    self->rom.ov = ov / 5;
    self->write_flags[3] = 1;
    self->write_flags[4] = 1;
}

/**
 * @brief 设置过充电恢复电压
 * @param self 设备实例指针
 * @param ovr 过充电恢复电压，单位：mV，范围：3000-5000mV，步长10mV
 */
static void sh367601b_config_set_ovr(*********_Device* self, unsigned short ovr)
{
    if (self == NULL) return;
    self->rom.ovr = ovr / 10;
    self->write_flags[4] = 1;
    self->write_flags[5] = 1;
}

/**
 * @brief 设置过放电恢复电压
 * @param self 设备实例指针
 * @param uvr 过放电恢复电压，单位：mV，范围：1000-5000mV，步长20mV
 */
static void sh367601b_config_set_uvr(*********_Device* self, unsigned short uvr)
{
    if (self == NULL) return;
    self->rom.uvr = uvr / 20;
    self->write_flags[6] = 1;
}

/**
 * @brief 设置低电压禁止充电电压
 * @param self 设备实例指针
 * @param lov 低压禁充电压，单位：mV，范围：500-2250mV，步长250mV，0=禁用
 */
static void sh367601b_config_set_lov(*********_Device* self, short lov)
{
    if (self == NULL) return;
    if (!lov) self->rom.lov = 0;
    else self->rom.lov = (lov - 500) / 250;
    self->write_flags[7] = 1;
}

/**
 * @brief 设置均衡进入延时
 * @param self 设备实例指针
 * @param balt 均衡延时，0=10分钟，1=30分钟
 */
static void sh367601b_config_set_balt(*********_Device* self, unsigned char balt)
{
    if (self == NULL || balt > 0x01) return;
    self->rom.balt = balt;
    self->write_flags[7] = 1;
}

/**
 * @brief 设置过放电保护延时
 * @param self 设备实例指针
 * @param uvt 延时设置，0=1s，1=4s，2=8s，3=16s
 */
static void sh367601b_config_set_uvt(*********_Device* self, unsigned char uvt)
{
    if (self == NULL || uvt > 0x03) return;
    self->rom.uvt = uvt;
    self->write_flags[7] = 1;
}

/**
 * @brief 设置过放电保护电压
 * @param self 设备实例指针
 * @param uv 过放电保护电压，单位：mV，范围：1000-5000mV，步长10mV
 */
static void sh367601b_config_set_uv(*********_Device* self, unsigned short uv)
{
    if (self == NULL) return;
    self->rom.uv = uv / 10;
    self->write_flags[7] = 1;
    self->write_flags[8] = 1;
}

/**
 * @brief 设置均衡开启电压
 * @param self 设备实例指针
 * @param balv 均衡开启电压，单位：mV，范围：3000-8000mV，步长20mV
 */
static void sh367601b_config_set_balv(*********_Device* self, unsigned short balv)
{
    if (self == NULL) return;
    self->rom.bald = balv / 20;
    self->write_flags[9] = 1;
}

/**
 * @brief 设置均衡开启压差
 * @param self 设备实例指针
 * @param bald 均衡压差，0=10mV，1=20mV，2=30mV，3=50mV
 */
static void sh367601b_config_set_bald(*********_Device* self, unsigned char bald)
{
    if (self == NULL || bald > 0x03) return;
    self->rom.bald = bald;
    self->write_flags[10] = 1;
}

/**
 * @brief 设置放电过流1保护电压
 * @param self 设备实例指针
 * @param ocd1v 过流1保护电压，单位：mV，范围：1575-9450mV，步长525mV，0=禁用
 */
static void sh367601b_config_set_ocd1v(*********_Device* self, unsigned short ocd1v)
{
    if (self == NULL) return;
    if (!ocd1v) self->rom.ocd1v = 0;
    self->rom.ocd1v = (ocd1v - 1575) / 525;
    self->write_flags[10] = 1;
}

/**
 * @brief 设置放电过流1保护延时
 * @param self 设备实例指针
 * @param ocd1t 延时设置，0=8ms，1=20ms，2=40ms，3=80ms
 */
static void sh367601b_config_set_ocd1t(*********_Device* self, unsigned char ocd1t)
{
    if (self == NULL || ocd1t > 0x03) return;
    self->rom.ocd1t = ocd1t;
    self->write_flags[10] = 1;
}

/**
 * @brief 设置短路保护延时
 * @param self 设备实例指针
 * @param sct 延时设置，0=70μs，1=100μs，2=200μs，3=400μs
 */
static void sh367601b_config_set_sct(*********_Device* self, unsigned char sct)
{
    if (self == NULL || sct > 0x03) return;
    self->rom.sct = sct;
    self->write_flags[11] = 1;
}

/**
 * @brief 设置放电过流2保护电压
 * @param self 设备实例指针
 * @param ocd2v 过流2保护电压档位，0-7对应不同电压等级
 */
static void sh367601b_config_set_ocd2v(*********_Device* self, unsigned char ocd2v)
{
    if (self == NULL || ocd2v > 0x07) return;
    self->rom.ocd2v = ocd2v;
    self->write_flags[11] = 1;
}

/**
 * @brief 设置放电过流2保护延时
 * @param self 设备实例指针
 * @param ocd2t 延时设置，0=8ms，1=20ms，2=40ms，3=80ms
 */
static void sh367601b_config_set_ocd2t(*********_Device* self, char ocd2t)
{
    if (self == NULL || ocd2t > 0x03) return;
    self->rom.ocd2t = ocd2t;
    self->write_flags[11] = 1;
}

/**
 * @brief 设置充电过流保护电压
 * @param self 设备实例指针
 * @param occv 充电过流保护电压，单位：mV，范围：350-5775mV，步长175mV，0=禁用
 */
static void sh367601b_config_set_occv(*********_Device* self, short occv)
{
    if (self == NULL) return;
    if (!occv) self->rom.occv = 0;
    else self->rom.occv = ((occv - 350) * 10 / 175 + 5) / 10;
    self->write_flags[12] = 1;
}

/**
 * @brief 设置充电过流保护延时
 * @param self 设备实例指针
 * @param occt 延时设置，0=8ms，1=20ms，2=40ms，3=80ms
 */
static void sh367601b_config_set_occt(*********_Device* self, unsigned char occt)
{
    if (self == NULL || occt > 0x03) return;
    self->rom.occt = occt;
    self->write_flags[12] = 1;
}

/**
 * @brief 设置充电高温保护阈值
 * @param self 设备实例指针
 * @param otc 充电高温保护温度，单位：℃，范围：-40到125℃
 */
static void sh367601b_config_set_otc(*********_Device* self, unsigned char otc)
{
    if (self == NULL) return;
    self->rom.otc = ntc_calculate_high_temp_reg(otc, ntc3435, NTC_TABLE_SIZE);
    self->write_flags[13] = 1;
}

/**
 * @brief 设置充电高温保护释放阈值
 * @param self 设备实例指针
 * @param otcr 充电高温保护释放温度，单位：℃，范围：-40到125℃
 */
static void sh367601b_config_set_otcr(*********_Device* self, unsigned char otcr)
{
    if (self == NULL) return;
    self->rom.otcr = ntc_calculate_high_temp_reg(otcr, ntc3435, NTC_TABLE_SIZE);
    self->write_flags[14] = 1;
}

/**
 * @brief 设置放电高温保护阈值
 * @param self 设备实例指针
 * @param otd 放电高温保护温度，单位：℃，范围：-40到125℃
 */
static void sh367601b_config_set_otd(*********_Device* self, unsigned char otd)
{
    if (self == NULL) return;
    self->rom.otd = ntc_calculate_high_temp_reg(otd, ntc3435, NTC_TABLE_SIZE);
    self->write_flags[15] = 1;
}

/**
 * @brief 设置放电高温保护释放阈值
 * @param self 设备实例指针
 * @param otdr 放电高温保护释放温度，单位：℃，范围：-40到125℃
 */
static void sh367601b_config_set_otdr(*********_Device* self, unsigned char otdr)
{
    if (self == NULL) return;
    self->rom.otdr = ntc_calculate_high_temp_reg(otdr, ntc3435, NTC_TABLE_SIZE);
    self->write_flags[16] = 1;
}

/**
 * @brief 设置充电低温保护阈值
 * @param self 设备实例指针
 * @param utc 充电低温保护温度，单位：℃，范围：-40到125℃
 */
static void sh367601b_config_set_utc(*********_Device* self, unsigned char utc)
{
    if (self == NULL) return;
    self->rom.utc = ntc_calculate_low_temp_reg(utc, ntc3435, NTC_TABLE_SIZE);
    self->write_flags[17] = 1;
}

/**
 * @brief 设置充电低温保护释放阈值
 * @param self 设备实例指针
 * @param utcr 充电低温保护释放温度，单位：℃，范围：-40到125℃
 */
static void sh367601b_config_set_utcr(*********_Device* self, unsigned char utcr)
{
    if (self == NULL) return;
    self->rom.utcr = ntc_calculate_low_temp_reg(utcr, ntc3435, NTC_TABLE_SIZE);
    self->write_flags[18] = 1;
}

/**
 * @brief 设置放电低温保护阈值
 * @param self 设备实例指针
 * @param utd 放电低温保护温度，单位：℃，范围：-40到125℃
 */
static void sh367601b_config_set_utd(*********_Device* self, unsigned char utd)
{
    if (self == NULL) return;
    self->rom.utd = ntc_calculate_low_temp_reg(utd, ntc3435, NTC_TABLE_SIZE);
    self->write_flags[19] = 1;
}

/**
 * @brief 设置放电低温保护释放阈值
 * @param self 设备实例指针
 * @param utdr 放电低温保护释放温度，单位：℃，范围：-40到125℃
 */
static void sh367601b_config_set_utdr(*********_Device* self, unsigned char utdr)
{
    if (self == NULL) return;
    self->rom.utdr = ntc_calculate_low_temp_reg(utdr, ntc3435, NTC_TABLE_SIZE);
    self->write_flags[20] = 1;
}

/* ==========================================内部工具函数========================================== */

/**
 * @brief CRC8校验函数
 * @param data 数据指针
 * @param len 数据长度
 * @return CRC8校验值
 * @note 使用多项式0x07进行CRC8校验，用于通信数据完整性验证
 */
static unsigned char sh367601b_crc8(unsigned char *data, unsigned char len)
{
    unsigned char crc = 0x00;
    unsigned char i, j;

    for (i = 0; i < len; i++) {
        crc ^= data[i];
        for (j = 0; j < 8; j++) {
            if (crc & 0x80) {
                crc = (crc << 1) ^ 0x07;  /* 多项式：x^8 + x^2 + x^1 + 1 */
            } else {
                crc <<= 1;
            }
        }
    }
    return crc;
}
/* ==========================================通信驱动模块实现========================================== */
/**
 * @brief 复位*********芯片
 * @note 发送复位命令到芯片，使芯片重新启动
 */
static void sh367601b_comm_reset(void)
{
    unsigned char cmd[5] = {0x1C, 0x0B, 0xBB, 0xCC, 0xFF};
    cmd[4] = sh367601b_crc8(cmd, 4);
    app_usart_fifo_push(cmd, 5);
}

/**
 * @brief 发送写命令到*********芯片
 * @note 将配置数据写入芯片ROM寄存器
 */
static void sh367601b_comm_write_command(void)
{
    unsigned char cmd[5] = {0x1C, 0x0A, 0xAA, 0xBB, 0xFF};
    cmd[4] = sh367601b_crc8(cmd, 4);
    app_usart_fifo_push(cmd, 5);
}

/**
 * @brief 写入单个数据到指定地址
 * @param data 要写入的数据
 * @param addr 目标地址
 */
static void sh367601b_comm_write_data(unsigned char data, unsigned char addr)
{
    unsigned char cmd[5] = {0x1C, 0x01, 0xFF, 0xFF, 0xFF};
    cmd[2] = addr;
    cmd[3] = data;
    cmd[4] = sh367601b_crc8(cmd, 4);
    app_usart_fifo_push(cmd, 5);
}

/**
 * @brief 读取ROM寄存器数据
 * @param addr 起始地址
 * @param recv_len 读取长度
 * @note 读取完成后需要手动调用解析函数：sh367601b_parser_parse_rom(device, received_data)
 */
static void sh367601b_comm_read_rom(unsigned char addr, unsigned char recv_len)
{
    unsigned char cmd[5] = {0x1C, 0x02, 0xFF, 0xFF, 0xFF};
    cmd[2] = addr;
    cmd[3] = recv_len;
    cmd[4] = sh367601b_crc8(cmd, 4);
    app_usart_fifo_push(cmd, 5);
}

/**
 * @brief 读取RAM寄存器数据
 * @param addr 起始地址
 * @param recv_len 读取长度
 * @note 读取完成后需要手动调用解析函数：sh367601b_parser_parse_ram(device, received_data)
 */
static void sh367601b_comm_read_ram(unsigned char addr, unsigned char recv_len)
{
    /* TODO: 调用底层通信模块读取RAM数据 */
    /* 示例：communication_read_ram(addr, recv_len, received_data); */
    /* 读取完成后需要手动调用解析函数：sh367601b_parser_parse_ram(device, received_data); */
    unsigned char cmd[5] = {0x1C, 0x03, 0xFF, 0xFF, 0xFF};
    cmd[2] = addr;
    cmd[3] = recv_len;
    cmd[4] = sh367601b_crc8(cmd, 4);
    app_usart_fifo_push(cmd, 5);
}

/* ==========================================数据解析模块实现========================================== */
/**
 * @brief 解析ROM寄存器数据
 * @param self 设备实例指针
 * @param data ROM数据缓冲区，长度应为21字节
 * @note 将接收到的ROM原始数据解析到设备ROM结构体中
 */
static void sh367601b_parser_parse_rom(*********_Device* self, unsigned char *data)
{
    if (self == NULL || data == NULL) return;
    /* 00H */
    self->rom.id = data[0];

    /* 01H */
    self->rom.enmosr = (data[1] >> 0x07) & 0x01;
    self->rom.chys   = (data[1] >> 0x06) & 0x01;
    self->rom.tc     = (data[1] >> 0x04) & 0x03;
    self->rom.cn     = (data[1] >> 0x00) & 0x0F;

    /* 02H */
    self->rom.bals  = (data[2] >> 0x07) & 0x01;
    self->rom.chs   = (data[2] >> 0x06) & 0x01;
    self->rom.ocra  = (data[2] >> 0x05) & 0x01;
    self->rom.eovr  = (data[2] >> 0x04) & 0x01;
    self->rom.euvr  = (data[2] >> 0x03) & 0x01;
    self->rom.eow   = (data[2] >> 0x02) & 0x01;
    self->rom.eot3  = (data[2] >> 0x01) & 0x01;
    self->rom.enmos = (data[2] >> 0x00) & 0x01;

    /* 03H,04H,05H */
    self->rom.ovt = (data[3] >> 0x06) & 0x03;
    self->rom.ov  = ((data[3] & 0x3F) << 0x04) | ((data[4] >> 4) & 0x0F);
    self->rom.ovr = ((data[4] & 0x01) << 0x08) | data[5];

    /* 06H */
    self->rom.uvr = data[6];

    /* 07H,08H */
    self->rom.lov  = (data[7] >> 0x05) & 0x07;
    self->rom.balt = (data[7] >> 0x04) & 0x01;
    self->rom.uvt  = (data[7] >> 0x01) & 0x03;
    self->rom.uv   = ((data[7] & 0x01) << 0x08) | data[8];

    /* 09H */
    self->rom.balv = data[9];

    /* 0AH */
    self->rom.bald  = (data[10] >> 0x06) & 0x03;
    self->rom.ocd1v = (data[10] >> 0x02) & 0x0F;
    self->rom.ocd1t = (data[10] >> 0x00) & 0x03;

    /* 0BH */
    self->rom.sct   = (data[11] >> 0x05) & 0x03;
    self->rom.ocd2v = (data[11] >> 0x02) & 0x07;
    self->rom.ocd2t = (data[11] >> 0x00) & 0x03;

    /* 0CH */
    self->rom.occv = (data[12] >> 0x02) & 0x1F;
    self->rom.occt = (data[12] >> 0x00) & 0x03;

    /* 0DH */
    self->rom.otc   = data[13];
    /* 0EH */
    self->rom.otcr = data[14];
    /* 0FH */
    self->rom.otd   = data[15];
    /* 10H */
    self->rom.otdr  = data[16];
    /* 11H */
    self->rom.utc   = data[17];
    /* 12H */
    self->rom.utcr  = data[18];
    /* 13H */
    self->rom.utd   = data[19];
    /* 14H */
    self->rom.utdr  = data[20];
}

/**
 * @brief 解析RAM寄存器数据
 * @param self 设备实例指针
 * @param data RAM数据缓冲区，长度应为46字节
 * @note 将接收到的RAM原始数据解析到设备RAM结构体中，包含电池电压、温度、电流等传感器数据
 */
static void sh367601b_parser_parse_ram(*********_Device* self, unsigned char *data)
{
    if (self == NULL || data == NULL) return;
    /* 40H */
    self->ram.l0v  = (data[0] >> 0x07) & 0x01;
    self->ram.ow   = (data[0] >> 0x06) & 0x01;
    self->ram.sc   = (data[0] >> 0x05) & 0x01;
    self->ram.occ  = (data[0] >> 0x04) & 0x01;
    self->ram.ocd1 = (data[0] >> 0x03) & 0x01;
    self->ram.ocd2 = (data[0] >> 0x02) & 0x01;
    self->ram.uv   = (data[0] >> 0x01) & 0x01;
    self->ram.ov   = (data[0] >> 0x00) & 0x01;

    /* 41H */
    self->ram.oti = (data[1] >> 0x04) & 0x01;
    self->ram.otd = (data[1] >> 0x03) & 0x01;
    self->ram.utd = (data[1] >> 0x02) & 0x01;
    self->ram.otc = (data[1] >> 0x01) & 0x01;
    self->ram.utc = (data[1] >> 0x00) & 0x01;

    /* 42H */
    self->ram.bal     = (data[2] >> 0x07) & 0x01;
    self->ram.pd      = (data[2] >> 0x06) & 0x01;
    self->ram.ctld    = (data[2] >> 0x05) & 0x01;
    self->ram.pro     = (data[2] >> 0x04) & 0x01;
    self->ram.chging  = (data[2] >> 0x03) & 0x01;
    self->ram.dsging  = (data[2] >> 0x02) & 0x01;
    self->ram.chg_fet = (data[2] >> 0x01) & 0x01;
    self->ram.dsg_fet = (data[2] >> 0x00) & 0x01;

    /* 43H */
    self->ram.l0v_flg  = (data[3] >> 0x07) & 0x01;
    self->ram.ow_flg   = (data[3] >> 0x06) & 0x01;
    self->ram.sc_flg   = (data[3] >> 0x05) & 0x01;
    self->ram.occ_flg  = (data[3] >> 0x04) & 0x01;
    self->ram.ocd1_flg = (data[3] >> 0x03) & 0x01;
    self->ram.ocd2_flg = (data[3] >> 0x02) & 0x01;
    self->ram.uv_flg   = (data[3] >> 0x01) & 0x01;
    self->ram.ov_flg   = (data[3] >> 0x00) & 0x01;

    /* 44H */
    self->ram.rst_flg = (data[4] >> 0x05) & 0x01;
    self->ram.oti_flg = (data[4] >> 0x04) & 0x01;
    self->ram.otd_flg = (data[4] >> 0x03) & 0x01;
    self->ram.utd_flg = (data[4] >> 0x02) & 0x01;
    self->ram.otc_flg = (data[4] >> 0x01) & 0x01;
    self->ram.utc_flg = (data[4] >> 0x00) & 0x01;

    /* 45H,46H */
    self->ram.temp1   = (data[5] << 8) | data[6];
    /* 47H,48H */
    self->ram.temp2   = (data[7] << 8) | data[8];
    /* 49H,4AH */
    self->ram.temp3   = (data[9] << 8) | data[10];
    /* 4BH,4CH */
    self->ram.tempn   = (data[11] << 8) | data[12];

    /* 4DH, 4EH */
    self->ram.cur   = ((data[13] << 8) | data[14]);
    /* 4FH,6EH */
    self->ram.cell1   = (data[15] << 8) | data[16];
    self->ram.cell2   = (data[17] << 8) | data[18];
    self->ram.cell3   = (data[19] << 8) | data[20];
    self->ram.cell4   = (data[21] << 8) | data[22];
    self->ram.cell5   = (data[23] << 8) | data[24];
    self->ram.cell6   = (data[25] << 8) | data[26];
    self->ram.cell7   = (data[27] << 8) | data[28];
    self->ram.cell8   = (data[29] << 8) | data[30];
    self->ram.cell9   = (data[31] << 8) | data[32];
    self->ram.cell10   = (data[33] << 8) | data[34];
    self->ram.cell11   = (data[35] << 8) | data[36];
    self->ram.cell12   = (data[37] << 8) | data[38];
    self->ram.cell13   = (data[39] << 8) | data[40];
    self->ram.cell14   = (data[41] << 8) | data[42];
    self->ram.cell15   = (data[43] << 8) | data[44];
    self->ram.cell16   = (data[45] << 8) | data[46];
}

/**
 * @brief 打印ROM配置信息
 * @param self 设备实例指针
 * @note 以格式化的方式打印所有ROM配置参数，包括保护阈值、延时设置等
 */
static void sh367601b_parser_print_rom(*********_Device* self)
{
    if (self == NULL) return;

    printf("=== ROM Configuration ===\n");
    printf("ID: 0x%02X\n", self->rom.id);
    printf("ENMOSR: %d, CHYS: %d, TC: %d, CN: %d\n",
           self->rom.enmosr, self->rom.chys, self->rom.tc, self->rom.cn);
    printf("BALS: %d, CHS: %d, OCRA: %d\n",
           self->rom.bals, self->rom.chs, self->rom.ocra);
    printf("EOVR: %d, EUVR: %d, EOW: %d, EOT3: %d, ENMOS: %d\n",
           self->rom.eovr, self->rom.euvr, self->rom.eow, self->rom.eot3, self->rom.enmos);
    printf("OVT: %d, OV: %d mV, OVR: %d mV\n",
           self->rom.ovt, self->converter.ov_to_voltage(self->rom.ov),
           self->converter.ovr_to_voltage(self->rom.ovr));
    printf("UVR: %d mV, LOV: %d mV\n",
           self->converter.uvr_to_voltage(self->rom.uvr),
           self->converter.lov_to_voltage(self->rom.lov));
    printf("BALT: %d, UVT: %d, UV: %d mV\n",
           self->rom.balt, self->rom.uvt, self->converter.uv_to_voltage(self->rom.uv));
    printf("BALV: %d mV, BALD: %d\n",
           self->converter.balv_to_voltage(self->rom.balv), self->rom.bald);
    printf("OCD1V: %d mV, OCD1T: %d, SCT: %d\n",
           self->converter.ocd1v_to_voltage(self->rom.ocd1v), self->rom.ocd1t, self->rom.sct);
    printf("OCD2V: %d mV, OCD2T: %d\n",
           self->converter.ocd2v_to_voltage(self->rom.ocd2v), self->rom.ocd2t);
    printf("OCCV: %d mV, OCCT: %d\n",
           self->converter.occv_to_voltage(self->rom.occv), self->rom.occt);
    printf("Temperature Protection: OTC=%d, OTCR=%d, OTD=%d, OTDR=%d\n",
           self->rom.otc, self->rom.otcr, self->rom.otd, self->rom.otdr);
    printf("Temperature Protection: UTC=%d, UTCR=%d, UTD=%d, UTDR=%d\n",
           self->rom.utc, self->rom.utcr, self->rom.utd, self->rom.utdr);
}

/**
 * @brief 打印RAM传感器数据
 * @param self 设备实例指针
 * @note 以格式化的方式打印所有传感器数据，包括保护状态、电池电压、温度、电流等
 */
static void sh367601b_parser_print_ram(*********_Device* self)
{
    if (self == NULL) return;

    printf("=== RAM Sensor Data ===\n");

    /* 保护状态信息 (40H) */
    printf("Protection Status:\n");
    printf("  L0V: %d, OW: %d, SC: %d, OCC: %d\n",
           self->ram.l0v, self->ram.ow, self->ram.sc, self->ram.occ);
    printf("  OCD1: %d, OCD2: %d, UV: %d, OV: %d\n",
           self->ram.ocd1, self->ram.ocd2, self->ram.uv, self->ram.ov);

    /* 温度保护状态 (41H) */
    printf("Temperature Protection:\n");
    printf("  OTI: %d, OTD: %d, UTD: %d, OTC: %d, UTC: %d\n",
           self->ram.oti, self->ram.otd, self->ram.utd, self->ram.otc, self->ram.utc);

    /* 系统状态 (42H) */
    printf("System Status:\n");
    printf("  BAL: %d, PD: %d, CTLD: %d, PRO: %d\n",
           self->ram.bal, self->ram.pd, self->ram.ctld, self->ram.pro);
    printf("  CHGING: %d, DSGING: %d, CHG_FET: %d, DSG_FET: %d\n",
           self->ram.chging, self->ram.dsging, self->ram.chg_fet, self->ram.dsg_fet);

    /* 保护标志位 (43H) */
    printf("Protection Flags:\n");
    printf("  L0V_FLG: %d, OW_FLG: %d, SC_FLG: %d, OCC_FLG: %d\n",
           self->ram.l0v_flg, self->ram.ow_flg, self->ram.sc_flg, self->ram.occ_flg);
    printf("  OCD1_FLG: %d, OCD2_FLG: %d, UV_FLG: %d, OV_FLG: %d\n",
           self->ram.ocd1_flg, self->ram.ocd2_flg, self->ram.uv_flg, self->ram.ov_flg);

    /* 温度保护标志位 (44H) */
    printf("Temperature Flags:\n");
    printf("  RST_FLG: %d, OTI_FLG: %d, OTD_FLG: %d, UTD_FLG: %d, OTC_FLG: %d, UTC_FLG: %d\n",
           self->ram.rst_flg, self->ram.oti_flg, self->ram.otd_flg,
           self->ram.utd_flg, self->ram.otc_flg, self->ram.utc_flg);

    /* 温度数据 (45H-4CH) */
    printf("Temperatures (Raw/Converted):\n");
    printf("  TEMP1: 0x%04X (%d), TEMP2: 0x%04X (%d)\n",
           self->ram.temp1, ntc_calculate_external_temp(self->ram.temp1, ntc3435, NTC_TABLE_SIZE),
           self->ram.temp2, ntc_calculate_external_temp(self->ram.temp2, ntc3435, NTC_TABLE_SIZE));
    printf("  TEMP3: 0x%04X (%d°C), TEMPN: 0x%04X (%d°C)\n",
           self->ram.temp3, ntc_calculate_external_temp(self->ram.temp3, ntc3435, NTC_TABLE_SIZE),
           self->ram.tempn, ntc_calculate_external_temp(self->ram.tempn, ntc3435, NTC_TABLE_SIZE));

    /* 电流数据 (4DH-4EH) */
    printf("Current: 0x%04X (%d mA)\n",
           self->ram.cur, current_calculate_from_adc(self->ram.cur, 1.0f, 90.0f, 26214.4f));

    /* 电池电压数据 (4FH-6EH) */
    printf("Cell Voltages (Raw/mV):\n");
    printf("  Cell1-4:   0x%04X(%d), 0x%04X(%d), 0x%04X(%d), 0x%04X(%d)\n",
           self->ram.cell1, self->ram.cell1, self->ram.cell2, self->ram.cell2,
           self->ram.cell3, self->ram.cell3, self->ram.cell4, self->ram.cell4);
    printf("  Cell5-8:   0x%04X(%d), 0x%04X(%d), 0x%04X(%d), 0x%04X(%d)\n",
           self->ram.cell5, self->ram.cell5, self->ram.cell6, self->ram.cell6,
           self->ram.cell7, self->ram.cell7, self->ram.cell8, self->ram.cell8);
    printf("  Cell9-12:  0x%04X(%d), 0x%04X(%d), 0x%04X(%d), 0x%04X(%d)\n",
           self->ram.cell9, self->ram.cell9, self->ram.cell10, self->ram.cell10,
           self->ram.cell11, self->ram.cell11, self->ram.cell12, self->ram.cell12);
    printf("  Cell13-16: 0x%04X(%d), 0x%04X(%d), 0x%04X(%d), 0x%04X(%d)\n",
           self->ram.cell13, self->ram.cell13, self->ram.cell14, self->ram.cell14,
           self->ram.cell15, self->ram.cell15, self->ram.cell16, self->ram.cell16);
}

/* ==========================================数据转换模块实现========================================== */
/**
 * @brief 过充电保护电压寄存器值转换为实际电压
 * @param ov 过充电保护电压寄存器值
 * @return 实际电压值，单位：mV
 */
static unsigned short sh367601b_converter_ov_to_voltage(unsigned short ov)
{
    return ov * 5;
}

/**
 * @brief 过充电恢复电压寄存器值转换为实际电压
 * @param ovr 过充电恢复电压寄存器值
 * @return 实际电压值，单位：mV
 */
static unsigned short sh367601b_converter_ovr_to_voltage(unsigned short ovr)
{
    return ovr * 10;
}

/**
 * @brief 均衡开启电压寄存器值转换为实际电压
 * @param balv 均衡开启电压寄存器值
 * @return 实际电压值，单位：mV
 */
static unsigned short sh367601b_converter_balv_to_voltage(unsigned short balv)
{
    return balv * 20;
}

/**
 * @brief 过放电保护电压寄存器值转换为实际电压
 * @param uv 过放电保护电压寄存器值
 * @return 实际电压值，单位：mV
 */
static unsigned short sh367601b_converter_uv_to_voltage(unsigned short uv)
{
    return uv * 10;
}

/**
 * @brief 过放电恢复电压寄存器值转换为实际电压
 * @param uvr 过放电恢复电压寄存器值
 * @return 实际电压值，单位：mV
 */
static unsigned short sh367601b_converter_uvr_to_voltage(unsigned short uvr)
{
    return uvr * 20;
}

/**
 * @brief 低压禁止充电电压寄存器值转换为实际电压
 * @param lov 低压禁充电压寄存器值
 * @return 实际电压值，单位：mV，0表示禁用
 */
static unsigned short sh367601b_converter_lov_to_voltage(unsigned short lov)
{
    if (!lov) return 0;
    return lov * 250 + 500;
}

/**
 * @brief 放电过流1保护电压寄存器值转换为实际电压
 * @param ocd1v 放电过流1保护电压寄存器值
 * @return 实际电压值，单位：mV，0表示禁用
 */
static unsigned short sh367601b_converter_ocd1v_to_voltage(unsigned short ocd1v)
{
    if (!ocd1v) return 0;
    return ocd1v * 525 + 1575;
}

/**
 * @brief 放电过流2保护电压寄存器值转换为实际电压
 * @param ocd2v 放电过流2保护电压寄存器值（0-7）
 * @return 实际电压值，单位：mV
 */
static unsigned short sh367601b_converter_ocd2v_to_voltage(unsigned short ocd2v)
{
    const unsigned short ocd2v_table[] = {0, 525, 1050, 1575, 2100, 2625, 3150, 3675};
    if (ocd2v > 7) return 0;
    return ocd2v_table[ocd2v];
}

/**
 * @brief 短路保护电压寄存器值转换为实际电压
 * @param ocd2vd 短路保护电压寄存器值（0-7）
 * @return 实际电压值，单位：mV
 */
static unsigned short sh367601b_converter_ocd2vd_to_voltage(unsigned short ocd2vd)
{
    const unsigned short ocd2vd_table[] = {0, 175, 350, 525, 700, 875, 1050, 1225};
    if (ocd2vd > 7) return 0;
    return ocd2vd_table[ocd2vd];
}

/**
 * @brief 充电过流保护电压寄存器值转换为实际电压
 * @param occv 充电过流保护电压寄存器值
 * @return 实际电压值，单位：mV，0表示禁用
 */
static unsigned short sh367601b_converter_occv_to_voltage(unsigned short occv)
{
    if (!occv) return 0;
    return (occv * 175 + 5) / 10 + 350;
}

/**
 * @brief 充电温度保护延时寄存器值转换为实际延时
 * @param tc 充电温度保护延时寄存器值
 * @return 实际延时时间，单位：秒
 */
static unsigned char sh367601b_converter_tc_to_delay(char tc)
{
    return tc * 2 + 3;
}



/* ==========================================BMS数据更新方法实现========================================== */
/**
 * @brief 处理ROM数据并更新到BMS系统配置
 * @param device 设备实例指针
 */
void sh367601b_update_bms_from_rom(*********_Device* device)
{
    if (device == NULL) return;
    VoltageManager *vmgr = &device->bms_system.voltage_mgr;
    ProtectionParameterManager *pmgr = &device->bms_system.protection_param_mgr;

    /* 更新保护参数 */
    pmgr->methods.process_protection_data(pmgr, device->converter.ov_to_voltage(device->rom.ov), device->converter.ovr_to_voltage(device->rom.ovr), device->rom.ovt,
    device->converter.uv_to_voltage(device->rom.uv), device->converter.uvr_to_voltage(device->rom.uvr), device->rom.uvt,
    device->converter.ocd1v_to_voltage(device->rom.ocd1v), device->rom.ocd1t,
    device->converter.ocd2v_to_voltage(device->rom.ocd2v), device->rom.ocd2t,
    device->converter.occv_to_voltage(device->rom.occv), device->rom.occt,
    ntc_calculate_temp_from_adc(device->rom.otc, ntc3435, NTC_TABLE_SIZE), ntc_calculate_temp_from_adc(device->rom.otcr, ntc3435, NTC_TABLE_SIZE),
    ntc_calculate_low_temp_from_reg(device->rom.utc, ntc3435, NTC_TABLE_SIZE), ntc_calculate_low_temp_from_reg(device->rom.utcr, ntc3435, NTC_TABLE_SIZE),
    ntc_calculate_temp_from_adc(device->rom.otd, ntc3435, NTC_TABLE_SIZE), ntc_calculate_temp_from_adc(device->rom.otdr, ntc3435, NTC_TABLE_SIZE),
    ntc_calculate_low_temp_from_reg(device->rom.utd, ntc3435, NTC_TABLE_SIZE), ntc_calculate_low_temp_from_reg(device->rom.utdr, ntc3435, NTC_TABLE_SIZE),
    device->converter.balv_to_voltage(device->rom.balv), device->rom.bald);

    /* 更新电池串数 */
    vmgr->data.battery_count = device->rom.cn + 6;
    vmgr->data.battery_count = vmgr->data.battery_count > 16 ? 16 : vmgr->data.battery_count;
    printf("*********: BMS data updated from ROM\n");
}


/**
 * @brief 从********* RAM数据更新BMS系统（自动计算所有衍生值）
 * @param device 设备实例指针
 */
void sh367601b_update_bms_from_ram(*********_Device* device)
{
    if (device == NULL) return;
    AlarmManager *amgr = &device->bms_system.alarm_mgr;
    /* 更新告警管理器数据 */
    amgr->methods.process_alarm_data(amgr, device->ram.otc_flg, device->ram.utc_flg,
    device->ram.otd_flg, device->ram.utd_flg, device->ram.occ_flg, device->ram.ocd1_flg, device->ram.ocd2_flg, 
    device->ram.uv_flg, device->ram.ov_flg);
    /* 更新状态管理器数据 */
    /* 更新充放电管理器数据 */
    /* 更新温度管理器数据 */
    /* 更新电池状态管理器数据 */
    /* 更新电压管理器数据 */
    /* 更新电流管理器数据 */
    printf("*********: BMS data update from RAM completed\n");
}

/**
 * @brief 将BMS保护参数更新到*********
 * @param self *********设备实例指针
 */
static void sh367601b_update_protection_parameters(*********_Device* self)
{
    if (self == NULL) return;

    ProtectionParameters* params = &self->bms_system.protection_param_mgr.data;

    /* 更新电压保护参数 */
    self->config.set_ov(self, params->overvoltage_protection);
    self->config.set_ovr(self, params->overvoltage_recovery);
    self->config.set_uv(self, params->undervoltage_protection);
    self->config.set_uvr(self, params->undervoltage_recovery);

    /* 更新温度保护参数 */
    self->config.set_otc(self, params->charge_high_temp_protection);
    self->config.set_otcr(self, params->charge_high_temp_recovery);
    self->config.set_utc(self, params->charge_low_temp_protection);
    self->config.set_utcr(self, params->charge_low_temp_recovery);
    self->config.set_otd(self, params->discharge_high_temp_protection);
    self->config.set_otdr(self, params->discharge_high_temp_recovery);
    self->config.set_utd(self, params->discharge_low_temp_protection);
    self->config.set_utdr(self, params->discharge_low_temp_recovery);

    printf("*********: Protection parameters updated from BMS system\n");
}


/**
 * @brief 初始化*********设备实例（自动初始化内嵌BMS系统）
 * @param device 设备指针
 * @return 0=成功，-1=失败
 */
int sh367601b_init(*********_Device* device)
{
    if (device == NULL) return -1;

    /* 清零数据成员 */
    memset(&device->rom, 0, sizeof(device->rom));
    memset(&device->ram, 0, sizeof(device->ram));
    memset(device->write_flags, 0, sizeof(device->write_flags));

    /* 初始化设备状态 */
    device->is_initialized = false;
    device->is_connected = false;

    /* 自动初始化内嵌的BMS数据管理系统 */
    if (bms_data_manager_init(&device->bms_system) != 0) {
        printf("*********: BMS data manager basic init failed\n");
        return -1;
    }

    /* 完整初始化BMS系统（使用ROM配置的参数） */
    if (device->bms_system.methods.init) 
        device->bms_system.methods.init(&device->bms_system);

    /* 初始化配置管理模块方法指针 */
    device->config.set_id = sh367601b_config_set_id;
    device->config.set_enmosr = sh367601b_config_set_enmosr;
    device->config.set_chys = sh367601b_config_set_chys;
    device->config.set_tc = sh367601b_config_set_tc;
    device->config.set_cn = sh367601b_config_set_cn;
    device->config.set_bals = sh367601b_config_set_bals;
    device->config.set_chs = sh367601b_config_set_chs;
    device->config.set_ocra = sh367601b_config_set_ocra;
    device->config.set_eovr = sh367601b_config_set_eovr;
    device->config.set_euvr = sh367601b_config_set_euvr;
    device->config.set_eow = sh367601b_config_set_eow;
    device->config.set_eot3 = sh367601b_config_set_eot3;
    device->config.set_enmos = sh367601b_config_set_enmos;
    device->config.set_ovt = sh367601b_config_set_ovt;
    device->config.set_ov = sh367601b_config_set_ov;
    device->config.set_ovr = sh367601b_config_set_ovr;
    device->config.set_uvr = sh367601b_config_set_uvr;
    device->config.set_lov = sh367601b_config_set_lov;
    device->config.set_balt = sh367601b_config_set_balt;
    device->config.set_uvt = sh367601b_config_set_uvt;
    device->config.set_uv = sh367601b_config_set_uv;
    device->config.set_balv = sh367601b_config_set_balv;
    device->config.set_bald = sh367601b_config_set_bald;
    device->config.set_ocd1v = sh367601b_config_set_ocd1v;
    device->config.set_ocd1t = sh367601b_config_set_ocd1t;
    device->config.set_sct = sh367601b_config_set_sct;
    device->config.set_ocd2v = sh367601b_config_set_ocd2v;
    device->config.set_ocd2t = sh367601b_config_set_ocd2t;
    device->config.set_occv = sh367601b_config_set_occv;
    device->config.set_occt = sh367601b_config_set_occt;
    device->config.set_otc = sh367601b_config_set_otc;
    device->config.set_otcr = sh367601b_config_set_otcr;
    device->config.set_otd = sh367601b_config_set_otd;
    device->config.set_otdr = sh367601b_config_set_otdr;
    device->config.set_utc = sh367601b_config_set_utc;
    device->config.set_utcr = sh367601b_config_set_utcr;
    device->config.set_utd = sh367601b_config_set_utd;
    device->config.set_utdr = sh367601b_config_set_utdr;

    /* 初始化通信驱动模块方法指针 */
    device->comm.reset = sh367601b_comm_reset;
    device->comm.write_command = sh367601b_comm_write_command;
    device->comm.write_data = sh367601b_comm_write_data;
    device->comm.read_rom = sh367601b_comm_read_rom;
    device->comm.read_ram = sh367601b_comm_read_ram;

    /* 初始化数据解析模块方法指针 */
    device->parser.parse_rom = sh367601b_parser_parse_rom;
    device->parser.parse_ram = sh367601b_parser_parse_ram;
    device->parser.print_rom = sh367601b_parser_print_rom;
    device->parser.print_ram = sh367601b_parser_print_ram;

    /* 初始化数据转换模块方法指针 */
    device->converter.ov_to_voltage = sh367601b_converter_ov_to_voltage;
    device->converter.ovr_to_voltage = sh367601b_converter_ovr_to_voltage;
    device->converter.balv_to_voltage = sh367601b_converter_balv_to_voltage;
    device->converter.uv_to_voltage = sh367601b_converter_uv_to_voltage;
    device->converter.uvr_to_voltage = sh367601b_converter_uvr_to_voltage;
    device->converter.lov_to_voltage = sh367601b_converter_lov_to_voltage;
    device->converter.ocd1v_to_voltage = sh367601b_converter_ocd1v_to_voltage;
    device->converter.ocd2v_to_voltage = sh367601b_converter_ocd2v_to_voltage;
    device->converter.ocd2vd_to_voltage = sh367601b_converter_ocd2vd_to_voltage;
    device->converter.occv_to_voltage = sh367601b_converter_occv_to_voltage;
    device->converter.tc_to_delay = sh367601b_converter_tc_to_delay;

    /* 初始化BMS数据更新方法指针 */
    device->bms_sync.update_realtime_data = sh367601b_update_bms_from_ram;  /* RAM实时数据更新方法 */
    device->bms_sync.update_protection_config = sh367601b_update_bms_from_rom;  /* ROM保护配置更新方法 */
    device->bms_sync.update_protection_parameters = sh367601b_update_protection_parameters;

    device->is_initialized = true;
    printf("SH367601XB: Device initialized with BMS system integration\n");
    return 0;
}
